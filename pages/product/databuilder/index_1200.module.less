// 小型移动端设备
@mobile-sm: ~'only screen and (max-width: 374.98PX)';
// 中型移动端设备
@mobile-md: ~'only screen and (min-width: 375PX) and (max-width: 599.98PX)';
// 大多数的移动设备
@mobile-sm-md: ~'@{mobile-sm}, @{mobile-md}';
// 大型移动端设备
@mobile-lg: ~'only screen and (max-width: 767.98PX)';
// 所有移动端设备 (组合变量)
@mobile: ~'@{mobile-sm}, @{mobile-md}, @{mobile-lg}';

// 小型平板设备
@tablet-sm: ~'only screen and (max-width: 991.98PX)';

// 所有平板设备
@tablet: ~'only screen and (max-width: 1199.98PX)';
// 只针对平板设备
@only-tablet: ~'only screen and (min-width: 768PX) and (max-width: 1199.98PX)';

// 一般桌面设备
@desktop-md: ~'only screen and (min-width: 1200PX) and (max-width: 1800.98PX)';
// 大型桌面设备
@desktop-lg: ~'only screen and (min-width: 1801PX)';
// 所有桌面设备 (组合变量)
@desktop: ~'@{desktop-md}, @{desktop-lg}';

// 静态资源路径
@basePath: "https://front-end.bj.bcebos.com/1.1.4";
/* 通用样式部分 */
// 背景图片
.bg-img(@image-url: 'path/to/image.jpg') {
    background-image: url(@image-url);
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    background-color: transparent;
}
// 渐变边框按钮
.gradient-border-text-button(@borderRadius: min(8px, 12PX), @bgImgUrl) {
    position: relative;
    .flex;
    align-items: center;
    justify-content: center;
    background-color: transparent;
    border-radius: @borderRadius;
    outline: none;
    border: none;
    cursor: pointer;
    opacity: 1;
    transition: opacity .5s ease;

    &::before {
        content: ' ';
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        .bg-img(@bgImgUrl);
        background-size: 100% 100%;
    }

    &:hover {
        opacity: .75;
    }

    .btn-text {
        position: relative;
        z-index: 1;
        background-color: transparent;
        font-family: PingFangSC-Semibold;
        text-align: center;
        font-weight: 600;
        background-image: linear-gradient(to right, #0060ff, #29acfe);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        user-select: none;
    }
}
.banner-gradient-border-text-button(@borderRadius: min(8px, 12PX)) {
    .gradient-border-text-button(
        @borderRadius,
        @bgImgUrl: "https://db-console-fe.bj.bcebos.com/gaiadb/20241010/banner_btn_bg.png"
    );
}
.flex {
    display: flex;
    -webkit-display: flex;
}
.max-width {
    max-width: min(1090px, 1635PX);

    @media @tablet {
        max-width: calc(100vw - 108PX);
    }

    @media @tablet-sm {
        max-width: calc(100vw - 64PX);
    }

    @media @mobile-lg {
        max-width: calc(100vw - 64PX);
    }

    @media @mobile-sm-md {
        max-width: calc(100vw - 32PX);
    }
}
// description 字体
.common-description {
    opacity: 0.7;
    font-family: PingFangSC-Regular;
    color: #091221;
    line-height: min(20px, 30PX);
    font-weight: 400;
    font-size: min(12px, 15PX);
    @media @tablet {
        font-size: 12PX;
        line-height: 20PX;
        max-width: 460PX;
    }
    @media @mobile-lg {
        font-size: 13PX;
        line-height: 22PX;
        max-width: 100%;
    }
}
.common-title {
    font-family: PingFangSC-Semibold;
    color: #091221;
    font-weight: 600;
}
// 移动端内容适配
.mobile-content-padding {
    @media @mobile-lg {
        padding: 0 32PX;
    }
    @media mobile-sm-md {
        padding: 0 16PX;
    }
}
:global {
    body {
        min-width: unset !important;
    }
    .databuilder-module {
        .databuilder-module-title {
            font-family: PingFangSC-Semibold;
            color: #091221;
            font-weight: 600;
            font-size: min(32px, 48PX);
            line-height: 40px;
            @media @tablet {
                font-size: 32PX;
            }
            @media @mobile-lg {
                font-size: 28PX;
            }
        }
        .databuilder-module-title,
        .databuilder-module-content {
            opacity: 1;
            transform: translateY(0);
            transition: opacity .5s ease-in-out, transform .5s ease-in-out;
        }
        .databuilder-below-module {
            opacity: 0;
            transform: translateY(60PX) translateZ(0PX);
            transition: opacity .5s ease-in-out, transform .5s ease-in-out;
        }
    }
    .footer-pc-container {
        min-width: unset !important;
        overflow: hidden !important;
        width: 100% !important;
    }
    html {
        font-size: calc(100vw / 120);
        @media screen and (max-width: 1200px) {
            font-size: 10px;
        }
        @media screen and (min-width: 1800px) {
            font-size: 15px;
        }
        box-sizing: border-box;
    }
    .cloud-header-pc {
        background: transparent !important;
        transition: background .3s ease-out;
        &:hover {
            background: #fff !important;
        }
    }
}
// 按钮
.btn-container {
    .flex;
    align-items: center;
    column-gap: min(16px, 24PX);

    @media @tablet {
        column-gap: 16PX;
    }

    @media @mobile-lg {
        column-gap: 12PX;
    }
}
.common-btn {
    cursor: pointer;
    font-family: PingFangSC-Semibold;
    width: min(112px, 168PX);
    height: min(40px, 60PX);
    line-height: min(40px, 60PX);
    border-radius: min(8px, 12PX);
    font-size: min(12px, 18PX);
    text-align: center;
    font-weight: 600;
    user-select: none;
    transition: opacity .3s linear;
    &:hover {
        opacity: 0.75;
    }
    @media @tablet {
        border-radius: 8PX;
        width: 112PX;
        height: 40PX;
        line-height: 40PX;
    }
    @media @mobile-lg {
        border-radius: 8PX;
        width: 116PX;
        height: 44PX;
        line-height: 44PX;
        font-size: 14PX;
        font-weight: 500;
    }
}
.free-btn {
    background-image: linear-gradient(269deg, #29ACFE 0%, #2468F2 100%);
    color: #FFFFFF;
}
.doc-btn {
    .banner-gradient-border-text-button(@borderRadius: min(8px, 12PX));
    .btn-text {
        background-image: linear-gradient(to right, #0060ff, #29acfe);
        background-clip: text;
        -webkit-background-clip: text;
        -moz-text-fill-color: transparent;
        -webkit-text-fill-color: transparent;
    }
}
.text-btn {
    cursor: pointer;
    background-image: linear-gradient(269deg, #29ACFE 0%, #2468F2 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -moz-text-fill-color: transparent;
    -webkit-text-fill-color: transparent;
    font-size: 12px;
    text-align: center;
    line-height: 20px;
    font-weight: 600;
    transition: all .2s;
    &:hover {
        opacity: 0.75;
    }
}
// header 模块
.fixed-header-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 48PX;
    background-color: #fff;
    z-index: 99999;
    .flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8PX 42PX 0 rgba(9, 18, 33, .08);
    transition: top 0.3s ease-in-out;
    @media @only-tablet {
        transition: none;
        z-index: -1;
    }
    @media @mobile-lg {
        top: 54PX;
        transition: none;
    }
    .fixed-header-inner-container {
        width: 100%;
        max-width: 1800PX;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        .flex;
        align-items: center;
        justify-content: center;
        @media @mobile-lg {
            justify-content: unset;
            padding-left: 32PX;
        }
        @media @mobile-sm-md {
            padding-left: 16PX;
        }
    }

    .left-container {
        position: absolute;
        left: 32PX;
        font-size: 16PX;
        color: #091221;
        font-weight: 600;

        @media @tablet {
            display: none;
        }
    }

    .header-module-list-container {
        .flex;
        align-items: center;
        column-gap: 32PX;
        font-family: PingFangSC-Regular;
        font-size: 14PX;
        color: #091221;
        letter-spacing: 0;
        cursor: pointer;
        height: 100%;
        line-height: 48PX;
        position: relative;
        @media @tablet {
            // width: calc(~"100% - 32px");
        }
        @media @tablet-sm {
            column-gap: 24PX;
        }

        .header-module-item {
            height: 100%;
            .header-module-text {
                font-weight: 400;
                white-space: nowrap;
                &:hover {
                    font-weight: 500;
                }
            }
        }

        .header-active-module-item {
            position: relative;


            .header-module-text {
                font-weight: 600;
            }
        }
    }

    .right-container {
        position: absolute;
        right: 32PX;
        background: #2468f2;
        border-radius: 6PX;
        width: 88PX;
        height: 32PX;
        font-family: PingFangSC-Regular;
        font-size: 14PX;
        color: #fff;
        text-align: center;
        line-height: 32PX;
        font-weight: 400;
        cursor: pointer;
        transition: background-color 0.3s linear;

        @media @tablet {
            display: none;
        }

        &:hover {
            background-color: #528eff;
        }
    }
}
.fixed-header-container-hidden {
    top: -48PX;
}
// banner和优势区域共用样式
.banner-advantage-module-container {
    background-position: 50%;
    background-color: #f1f4fd;
    background-size: cover;
}
// banner 适配 √
.banner-module-container {
    width: 100%;
    max-width: 1800PX;
    margin: 0 auto;
    .bg-img("@{basePath}/img_banner.jpg");
    background-position: center top;
    @media @mobile-lg {
        .flex;
        flex-direction:column;
        align-items: flex-start;
        padding: 60PX 0 196PX;
        .bg-img("@{basePath}/img_mobile_banner_600.png");
        background-position: right bottom;
    }

    @media @mobile-md {
        background-size: auto 600PX;
        background-position: center bottom;
    }

    @media @mobile-sm-md {
        padding: 40PX 0 194PX;
    }

    @media @mobile-sm {
        .bg-img("@{basePath}/img_mobile_banner375.png");
    }
    .banner-module-content {
        height: min(320px, 480PX);
        padding: 0 min(110px, 165PX);
        .flex;
        flex-direction: column;
        justify-content: center;
        @media @tablet {
            padding: 0 55PX;
        }
        @media @tablet-sm {
            padding: 0 32PX;
        }
        @media @mobile {
            height: auto;
            padding-top: 60Px;
        }
        @media @mobile-sm-md {
            padding: 0 24PX;
            padding-top: 40Px;
        }
        .banner-title {
            font-family: PingFangSC-Semibold;
            font-size: min(40px, 60PX);
            line-height: min(48px, 72PX);
            margin-bottom: min(16px, 24PX);
            color: #091221;
            font-weight: 600;
            @media @tablet {
                font-size: 40PX;
                line-height: 48PX;
                margin-bottom: 16PX;
            }

            @media @mobile-lg {
                font-size: 36PX;
                line-height: 48PX;
                margin-bottom: 12PX;
            }
        }
        .banner-description {
            .common-description;
            font-size: min(12px, 15PX);
            max-width: min(460px, 690PX);
        }
        .banner-btns {
            margin-top: min(24px, 36PX);
            .btn-container;
            @media @mobile-lg {
                margin-top: 20PX;
            }
        }
    }
}
// 优势区域 - 非移动端
.advantage-module-container {
    width: 100%;
    max-width: 1800PX;
    position: relative;
    left: 50%;
    transform: translate(-50%);
    margin-top: min(6px, 9PX);
    padding: 0 min(110px, 165PX);
    padding-bottom: min(48px, 72PX);
    .flex;
    align-items: flex-start;
    column-gap: min(34px, 50PX);
    @media @tablet {
        padding-left: 55PX;
        padding-right: 55PX;
        gap: 22PX;
    }

    @media @tablet-sm {
        padding-left: 32PX;
        padding-right: 32PX;
        gap: 18PX;
    }
    @media @mobile-lg {
        column-gap: unset;
    }
    .advantage-item-container {
        position: relative;
        max-width: min(250px, 375Px);
        .flex;
        flex-direction: column;
        align-items: flex-start;
        flex: 1;
        flex-shrink: 0;
        @media @tablet {
            max-width: none;
        }
        @media @mobile-lg {
            &:not(:last-child) {
                margin-right: 16PX;
            }
            &:not(:first-child) {
                margin-left: 16PX;
            }
            &::after {
                position: absolute;
                right: -16PX;
                top: calc(~"50% - 18PX");
                content: "";
                width: 1PX;
                height: 36PX;
                opacity: 0.08;
                background: #091221;
                display: block;
            }
            &:last-child {
                &::after {
                    display: none;
                }
            }
        }
        .top-container {
            .flex;
            flex-wrap: nowrap;
            align-items: flex-end;
            margin-bottom: min(8px, 12PX);
            @media @mobile-lg {
                flex-direction: column;
            }
            .advantage-title {
                font-family: PingFangSC-Semibold;
                font-size: min(16px, 24PX);
                color: #091221;
                line-height: min(24px,36PX);
                font-weight: 600;
                @media @tablet {
                    font-size: 16PX;
                    line-height: 24PX;
                }
                @media @mobile-lg {
                    font-size: 14PX;
                    line-height: 22PX;
                }
            }
            .advantage-highlight-text {
                font-family: DINAlternate-Bold;
                font-size: min(28px, 42PX);
                color: #091221;
                line-height: min(32px, 48PX);
                font-weight: 700;
                background-image: linear-gradient(to right, #0060ff, #29acfe);
                background-clip: text;
                -webkit-background-clip: text;
                -moz-text-fill-color: transparent;
                -webkit-text-fill-color: transparent;
                @media @tablet-sm {
                    font-size: 28PX;
                    line-height: 32PX;
                }
                @media @mobile-lg {
                    font-size: 20PX;
                    line-height: 28PX;
                }
            }
            .left-text {
                margin-right: min(4px, 6PX);
            }
            .right-text {
                margin-left: min(4px, 6PX);
            }
        }
        .advantage-description {
            .common-description;
            font-size: min(12px, 15PX);
        }
    }
}
// 优势区域 - 移动端
.mobile-advantage-container {
    .flex;
    align-items: stretch;
    padding: 0 32PX;
    margin-top: 24PX;

    @media @mobile-sm-md {
        padding: 0 24PX;
    }

    .mobile-advantage-item-container {
        .flex;
        flex-direction: column;
        align-items: flex-start;
        padding: 0 18PX;
        position: relative;

        @media @mobile-sm-md {
            padding: 0 16PX;
        }

        &::after {
            content: ' ';
            width: 1PX;
            height: 36PX;
            background-color: rgba(9, 18, 33, 0.08);
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
        }

        &:last-child {
            &::after {
                display: none;
            }
        }

        &:first-child {
            padding-left: 0;
        }

        .advantage-title {
            font-family: PingFangSC-Semibold;
            font-size: 14PX;
            color: #091221;
            line-height: 22PX;
            font-weight: 600;
            margin-bottom: 2PX;
            white-space: nowrap;
        }
        .advantage-highlight-font {
            font-size: 20PX;
            line-height: 28PX;
            font-weight: 700;
        }
        .advantage-top-font {
            font-family: PingFangSC-Semibold !important;
            font-size: 14PX;
            margin-bottom: 2PX;
            line-height: 22PX;
            font-weight: 600;
        }
        .advantage-highlight-text {
            font-family: DINAlternate-Bold;
            color: #091221;
            background-image: linear-gradient(to right, #0060ff, #29acfe);
            background-clip: text;
            -webkit-background-clip: text;
            -moz-text-fill-color: transparent;
            -webkit-text-fill-color: transparent;
        }
    }
}
// 能力覆盖 √
.structure-module-container {
    width: 100%;
    background-image: linear-gradient(180deg,rgba(241,244,253,0) 0%,#fff 50%);
    .structure-module-inner-container {
        width: 100%;
        padding-top: min(40px, 60PX);
        max-width: min(1146px, 1719PX);
        position: relative;
        left: 50%;
        transform: translateX(-50%);
        .flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        border-top-left-radius: min(16px, 24PX);
        border-top-right-radius: min(16px, 24PX);
        background: #FFFFFF;
        @media @tablet {
            padding-top: 40PX;
            max-width: calc(100vw - 54PX);
            border-top-left-radius: 16PX;
            border-top-right-radius: 16PX;
        }

        @media @tablet-sm {
            padding-top: 40PX;
            max-width: calc(100vw - 32PX);
        }

        @media @mobile-lg {
            max-width: 100%;
            border-top-left-radius: 24PX;
            border-top-right-radius: 24PX;
            margin-top: -20PX;
        }

        @media @mobile-sm-md {
            max-width: 100%;
        }
        .structure-module-title {
            margin-bottom: min(32px, 48PX);
            text-align: center;
            @media @mobile-lg {
                margin-bottom: 24PX;
            }
        }
        .structure-module-content {
            .structure-img {
                width: min(1146px, 1260PX);
                width: 840px;
                position: relative;
                left: 50%;
                transform: translateX(-50%);
                @media @tablet {
                    width: calc(~"100% - 98PX");
                }
                @media @tablet-sm {
                    width: calc(~"100% - 56PX");
                }
                @media @mobile-lg {
                    width: calc(~"100% - 64PX");
                }
                @media @mobile-sm-md {
                    width: calc(~"100% - 32PX");
                }
            }
        }
    }
}
// 核心能力
.core-ability-module-container {
    // width: 100%;
    width: min(1090px, 1635PX);
    margin-top: min(64px, 96PX);
    position: relative;
    left: 50%;
    transform: translateX(-50%);
    .flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    @media @tablet {
        width: calc(~"100vw - 110PX");
    }
    @media @tablet-sm {
        width: calc(~"100vw - 64PX");
    }
    @media @mobile-lg {
        margin-top: 40PX;
    }
    @media @mobile-sm-md {
        width: calc(~"100vw - 32PX");
        margin-top: 64PX;
    }
    .core-ability-module-title {
        margin-bottom: min(32px, 48PX);
        @media @mobile-lg {
            margin-bottom: 24PX;
        }
    }
    .core-ability-module-content {
        width: 100%;
        .flex;
        flex-direction: column;
        .left-title {
            .common-title;
            font-size: min(24px, 36PX);
            line-height: min(32px, 48PX);
            margin-bottom: min(20px, 30PX);
        }
        .iteration-container {
            width: 100%;
            margin-bottom: min(14px, 21PX);
            background-image: linear-gradient(119deg, rgba(231,230,255,0.32) 0%, rgba(217,238,255,0.89) 93%);
            border-radius: min(14px, 21PX);
            .flex;
            flex: 1;
            @media @tablet {
                border-radius: 14PX;
                height: 388PX;
                margin-bottom: 14PX;
            }
            @media @mobile-lg {
                margin-bottom: 10PX;
                flex-direction: column;
                align-items: center;
            }
            .left-container {
                padding: min(44px, 66PX) 0;
                padding-left: min(28px, 42PX);
                .flex;
                flex-direction: column;
                align-items: flex-start;
                @media @tablet {
                    padding: 44PX 0;
                    padding-left: 28PX;
                }
                @media @tablet-sm {
                    padding: 42PX 0;
                    padding-left: 20PX;
                }
                @media @mobile-lg {
                    padding: 20PX;
                    padding-bottom: 0;
                }
                .card-container {
                    .flex;
                    flex-wrap: wrap;
                    gap: min(8px, 12PX);
                    @media @tablet-sm {
                        gap: 12PX;
                    }
                    @media @mobile-lg {
                        gap: 10PX;
                    }
                    .left-card {
                        width: calc(~"50% - 8px");
                        padding: min(12px, 18PX);
                        background: rgba(255,255,255,0.40);
                        box-shadow: inset 0px 1px 0px 0px rgba(255,255,255,1);
                        border-radius: 8px;
                        @media @mobile-lg {
                            width: 100%;
                        }
                        .card-title {
                            .common-title;
                            font-size: min(14px, 21PX);
                            line-height: min(20px, 30PX);
                            margin-bottom: min(8px, 11PX);
                            .flex;
                            align-items: center;
                            @media @mobile-lg {
                                font-size: 15PX;
                                line-height: 22PX;
                                margin-bottom: 6PX;
                            }
                            .card-icon {
                                width: min(16px, 24PX);
                                margin-right: min(8px, 12PX);
                                box-shadow: 0 2px 2px 0 rgba(36, 104, 242, 0.2);
                                border-radius: 5px;
                            }
                        }
                        .left-description {
                            .common-description;
                            font-size: min(12px, 16PX);
                            margin-bottom: 0;
                        }
                    }
                }
                .free-btn {
                    margin-top: 20px;
                    height: min(36px, 54PX);
                    line-height: min(36px, 54PX);
                    @media @mobile-lg {
                        width: 130PX;
                        height: 40PX;
                        line-height: 40PX;
                    }
                }
            }
            .right-img {
                width: min(468px, 702PX);
                object-fit: contain;
                @media @tablet {
                    width: 320PX;
                }
                @media @tablet-sm {
                    width: 280PX;
                }
                @media @mobile-lg {
                    width: 343PX;
                }
                @media @mobile-sm-md {
                    width: 100%;
                }
            }
        }
        .abilitylist-container {
            width: 100%;
            .flex;
            gap: min(14px, 21PX);
            @media @mobile-lg {
                flex-direction: column;
                gap: 10px;
            }
            .item-container {
                width: min(354px, 531PX);
                background: #F1F6FD;
                border-radius: 14px;
                .flex;
                flex-direction: column;
                justify-content: space-between;
                align-items: center;
                flex: 1;
                flex-shrink: 0;
                @media @tablet {
                    width: 284PX;
                }
                @media @tablet-sm {
                    width: 225PX;
                }
                @media @mobile-lg {
                    width: 100%;
                    flex-direction: column;
                }
                .ability-item {
                    width: 100%;
                    padding: 0 min(28px, 42PX);
                    padding-top: min(24px, 36PX);
                    .flex;
                    flex-direction: column;
                    @media @tablet-sm {
                        padding: 0 20PX;
                        padding-top: 24PX;
                    }
                    .left-title {
                        text-align: left;
                        margin-bottom: min(16px, 24PX);
                        font-size: min(22px, 33PX);
                        line-height: min(30px, 45PX);
                        @media @mobile-lg {
                            margin-bottom: 12PX;
                        }
                    }
                    .content-container {
                        .content-item {
                            &:not(:last-child) {
                                margin-bottom: min(14px, 21PX);
                                @media @mobile-lg {
                                    margin-bottom: 12PX;
                                }
                            }
                            .content-item-title {
                                margin-bottom: min(4px, 6PX);
                                .flex;
                                align-items: center;
                                @media @mobile-lg {
                                    margin-bottom: 8PX;
                                }
                                .dot {
                                    display: inline-block;
                                    width: min(4px, 6PX);
                                    height: min(4px, 6PX);
                                    background-color: #091221;
                                    border-radius: 50%;
                                    margin-right: min(6px, 9PX);
                                }
                                .text {
                                    .common-title;
                                    font-size: min(12px, 18PX);
                                    line-height: min(20px, 30PX);
                                    @media @mobile-lg {
                                        font-size: 14PX;
                                        line-height: 20PX;
                                    }
                                }
                            }
                            .content-item-description {
                                .common-description;
                                font-size: min(12px, 16PX);
                            }
                        }
                    }
                }
                .item-image {
                    bottom: 0;
                    width: 100%;
                    @media @mobile-lg {
                        width: 284PX;
                        margin-top: 16PX;
                    }
                }
            }
        }
    }
}

// 应用场景
.apply-scene-module-container {
    position: relative;
    .flex;
    flex-direction: column;
    align-items: center;
    margin-top: min(62px, 96PX);
    padding: min(40px, 60PX);
    .bg-img("@{basePath}/img_yingyongchangjing_bg.jpg");
    border-radius: min(24px, 36PX);

    @media @tablet {
        margin-top: 64PX;
        padding: 40PX 55PX;
        border-radius: 24PX;
    }

    @media @tablet-sm {
        margin-top: 64PX;
        padding: 40PX 32PX;
        border-radius: 24PX;
    }

    @media @mobile-lg {
        margin-top: 40PX;
        padding: 40PX 32PX;
        border-radius: 16PX;
    }

    @media @mobile-sm-md {
        margin-top: 40PX;
        padding: 40PX 16PX;
    }
    // 格子背景
    .scene-module-square-bg-container {
        background-image: url('https://db-console-fe.bj.bcebos.com/gaiadb/20240924/img_gezi.png');
        background-size: cover;
        height: min(182px, 274PX);
        width: 100%;
        position: absolute;
        top: 0PX;

        @media @tablet {
            height: 182PX;
        }
    }
    .apply-scene-module-title {
        color: #FFFFFF;
    }
    .scene-tabs-container {
        background: rgba(24, 91, 199, .2);
        border-radius: min(20px, 30PX);
        .flex;
        align-items: center;
        height: min(36px, 54PX);
        margin-top: min(40px, 48PX);
        box-sizing: border-box;
        padding: 0 min(4px, 6PX);
        position: relative;
        z-index: 10;

        @media @tablet {
            width: 608PX;
            height: 36PX;
            margin-top: 26PX;
            padding: 0 4PX;
        }

        @media @mobile-lg {
            height: 44PX;
            margin-top: 20PX;
            padding: 0 8PX;
            width: 100%;
            overflow-x: auto;
            gap: 10PX;
            border-radius: 22PX;

            &::-webkit-scrollbar {
                display: none;
            }
        }

        .scene-tab-item {
            flex: 1;
            text-align: center;
            cursor: pointer;
            font-family: PingFangSC-Semibold;
            font-size: min(12px, 18PX);
            color: rgba(255, 255, 255, 0.8);
            text-align: center;
            font-weight: 600;
            width: min(160px, 240PX);
            min-width: fit-content;
            height: min(28px, 42PX);
            line-height: min(28px, 42PX);
            user-select: none;
            transition: color .3s linear;
            white-space: nowrap;

            &:hover {
                color: rgba(255, 255, 255, 1);
            }

            @media @tablet {
                font-size: 12PX;
                height: 16PX;
                line-height: 16PX;
            }

            @media @mobile-lg {
                font-size: 13PX;
                height: 20PX;
                line-height: 20PX;
            }
        }

        .scene-active-tab-item {
            color: #fff;
        }

        .scene-tab-item-slider {
            background: rgba(255, 255, 255, 0.12);
            border-radius: 16px;
            color: #fff;
            position: absolute;
            transform: translateX(min(4px, 6PX));
            left: 0;
            width: calc((100% - min(4px, 6PX) * 2) / 4);
            height: min(28px, 42PX);
            transition: left 0.3s ease-in-out, width 0.3s ease-in-out;
            @media @tablet {
                transform: translateX(4PX);
                width: calc((100% - 4PX * 2) / 4);
                height: 28PX;
            }

            @media @mobile-lg {
                height: 36PX;
                border-radius: 18PX;
            }
        }
    }
    .scene-content-container {
        width: 100%;
        .flex;
        align-items: stretch;
        margin-top: min(20px, 30PX);
        .max-width;

        @media @tablet {
            margin-top: 24PX;
        }

        @media @mobile-lg {
            margin-top: 20PX;
        }

        .scene-item-content-container {
            width: 100%;
            height: min(340px, 480PX);
            .flex;
            align-items: flex-start;
            justify-content: space-between;
            background: #ffffff;
            border-radius: min(14px, 21PX);
            box-sizing: border-box;

            @media @tablet {
                border-radius: 14PX;
            }

            @media @tablet-sm {
                border-radius: 16PX;
                padding: 32PX;
                .flex;
                flex-direction: column;
                align-items: flex-start;
                height: unset;
            }

            @media @mobile {
                padding: 20PX;
            }

            .left-container {
                .flex;
                flex-direction: column;
                justify-content: center;
                padding: min(32px, 48PX) min(36px, 54PX);

                @media @tablet {
                    padding: 32PX 24PX;
                }

                @media @tablet-sm {
                    padding: 0;
                }

                @media @mobile-lg {
                    padding: 0;
                }

                .scene-content-title {
                    font-family: PingFangSC-Semibold;
                    font-size: min(24px, 36PX);
                    color: #091221;
                    line-height: min(32px, 48PX);
                    font-weight: 600;
                    margin-bottom: min(20px, 30PX);

                    @media @tablet {
                        font-size: 24PX;
                        line-height: 32PX;
                        margin-bottom: 20PX;
                    }

                    @media @mobile-lg {
                        font-size: 22PX;
                        line-height: 36PX;
                        margin-bottom: 10PX;
                    }
                }

                .scene-content-description-container {
                    width: min(460px, 690PX);
                    .scene-content-title-description {
                        font-family: PingFangSC-Medium;
                        font-size: min(12px, 18PX);
                        color: #091221;
                        line-height: min(20px, 30PX);
                        font-weight: 600;
                        margin-bottom: min(4px, 6PX);
                        @media @mobile-lg {
                            font-weight: 500;
                        }
                        @media @mobile {
                            font-size: 14PX;
                        }
                    }

                    .scene-content-normal-description {
                        white-space: pre-line;
                        opacity: 0.7;
                        font-family: PingFangSC-Regular;
                        font-size: min(12px, 16PX);
                        color: #091221;
                        line-height: min(20px, 30PX);
                        font-weight: 400;
                        margin-bottom: min(20px, 30PX);

                        @media @tablet {
                            font-size: 12PX;
                            line-height: 20PX;
                            margin-bottom: 20PX;
                        }

                        @media @mobile-lg {
                            width: 100%;
                            font-size: 13PX;
                            line-height: 20PX;
                            margin-bottom: 16PX;

                            &:first-of-type {
                                margin-bottom: 4PX;
                            }
                        }
                    }

                    @media @tablet {
                        width: 100%;
                    }
                }
                .free-btn {
                    width: min(116px, 174PX);
                    height: min(36px, 54PX);
                    line-height: min(36px, 54PX);
                    @media @mobile-lg {
                        width: 130PX;
                        height: 40PX;
                        line-height: 40PX;
                    }
                }
            }

            .right-container {
                height: 100%;
                padding: min(8px, 12PX);
                padding-left: 0;

                .scene-content-img {
                    width: 100%;
                    height: 100%;
                    border-radius: min(8px, 12PX);
                    overflow: hidden;
                }

                @media @tablet {
                    .flex;
                    align-items: center;
                    width: 454PX;
                    flex-shrink: 0;
                }

                @media @tablet-sm {
                    padding: 0;
                    margin-top: 20PX;
                    width: unset;
                    flex-shrink: unset;
                }

                @media @mobile {
                    margin-top: 0;
                }

                @media @mobile-lg {
                    width: 100%;
                    margin-top: 20PX;
                }
            }
        }
    }
}

// 使用引导 √
.guide-part-module-container {
    width: 100%;
    height: min(335px, 504PX);
    .flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .bg-img("@{basePath}/img_footer_bg.jpg");
    @media @tablet {
        height: 337PX;
    }
    @media @mobile-lg {
        height: 324PX;
    }
    .guide-part-module-title {
        .common-title;
        font-size: min(32px, 48PX);
        line-height: min(40px, 60PX);
        margin-bottom: min(12px, 18PX);
        text-align: center;
        @media @mobile-lg {
            font-size: 28PX;
            line-height: 40PX;
            margin-bottom: 16PX;
        }
    }
    .guide-description {
        .common-description;
        font-size: min(12px, 18PX);
    }
    .guide-btns {
        margin-top: min(20px, 30PX);
        .btn-container;
        .common-btn {
            width: min(120px, 188PX);
            height: min(44px, 66PX);
            line-height: min(44px, 66PX);
        }
    }
}
.fade-in {
    opacity: 1;
    transform: translateY(0);
    transition: all 0.6s ease;
}

// 桌面设备按钮显示控制
.desktop-only-btn {
    @media @desktop {
        display: flex;
    }
    // 非桌面设备隐藏
    display: none;
}

// 非桌面设备按钮显示控制
.non-desktop-btn {
    @media @desktop {
        display: none;
    }
    // 非桌面设备显示
    display: flex;
}